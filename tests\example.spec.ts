
import { test, expect } from '@playwright/test';

test('homepage shows popular movies', async ({ page }) => {
  await page.goto('https://debs-obrien.github.io/playwright-movies-app');

  // Check for the main heading "Popular"
  await expect(page.getByRole('heading', { name: 'Popular', level: 1 })).toBeVisible();

  // Check for the subheading "movies"
  await expect(page.getByRole('heading', { name: 'movies', level: 2 })).toBeVisible();

  // Check for the movie list
  await expect(page.getByRole('list', { name: 'movies' })).toBeVisible();
});